"""Tests for the API service module using async approach."""

import os
from unittest.mock import <PERSON><PERSON>ock, patch

import pytest
from fastapi import <PERSON>TT<PERSON><PERSON>x<PERSON>, UploadFile
from starlette.status import (
    HTTP_400_BAD_REQUEST,
    HTTP_403_FORBIDDEN,
    HTTP_500_INTERNAL_SERVER_ERROR,
)

from cneutral_doc.api.parser.service import (
    ParsedDocumentResponse,
    parse_document,
    verify_api_key,
)
from cneutral_doc.document.parser.base import Page, ParsedDocument, Table
from cneutral_doc.document.parser.marker import MarkerParser


# Helper class for mocking async methods
class AsyncMock(MagicMock):
    async def __call__(self, *args, **kwargs):
        return super(AsyncMock, self).__call__(*args, **kwargs)


@pytest.fixture
def mock_api_key():
    """Mock API key for testing."""
    with patch.dict(os.environ, {"CNEUTRAL_API_KEY": "test-api-key"}):
        yield "test-api-key"


@pytest.fixture
def mock_pdf_file(tmp_path):
    """Create a mock PDF file for testing."""
    pdf_path = tmp_path / "test.pdf"
    pdf_path.write_bytes(b"%PDF-1.5\nMock PDF content")
    return pdf_path


@pytest.fixture
def mock_parsed_document():
    """Create a mock parsed document for testing."""
    return ParsedDocument(
        name="test_document",
        pages=[
            Page(
                content="Test page content", page_number=1, metadata={"source": "test"}
            ),
            Page(content="Another test page", page_number=2, metadata={}),
        ],
        metadata={"title": "Test Document", "pages": 2},
        raw_data="Raw test data",
        tables=[Table(content="Test table content", index=0)],
    )


@pytest.fixture
def mock_parser():
    """Create a mock MarkerParser instance."""
    parser = MarkerParser()
    return parser


@pytest.mark.asyncio
async def test_verify_api_key_valid(mock_api_key):
    """Test API key verification with valid key."""
    with patch("cneutral_doc.api.parser.service.API_KEY", "test-api-key"):
        result = await verify_api_key(mock_api_key)
        assert result == mock_api_key


@pytest.mark.asyncio
async def test_verify_api_key_invalid():
    """Test API key verification with invalid key."""
    with patch("cneutral_doc.api.parser.service.API_KEY", "test-api-key"):
        with pytest.raises(HTTPException) as exc_info:
            await verify_api_key("wrong-key")
        assert exc_info.value.status_code == HTTP_403_FORBIDDEN
        assert "Invalid or missing API key" in exc_info.value.detail


@pytest.mark.asyncio
async def test_parse_document_success(
    mock_api_key, mock_pdf_file, mock_parsed_document, mock_parser
):
    """Test successful document parsing."""
    # Create a new parser with HTML format
    html_parser = MarkerParser(format="html")

    with patch(
        "cneutral_doc.api.parser.service.MarkerParser", return_value=html_parser
    ), patch.object(html_parser, "parse", return_value=mock_parsed_document):

        # Create mock file
        mock_file = MagicMock(spec=UploadFile)
        mock_file.filename = mock_pdf_file.name
        mock_file.read = AsyncMock(return_value=mock_pdf_file.read_bytes())

        # Test the endpoint
        response = await parse_document(
            request=MagicMock(),
            file=mock_file,
            isolate_tables=True,
            format="html",
            api_key=mock_api_key,
        )

        # Verify response
        assert isinstance(response, ParsedDocumentResponse)
        assert len(response.pages) == len(mock_parsed_document.pages)
        assert response.metadata == mock_parsed_document.metadata
        assert html_parser.config["output_format"] == "html"


@pytest.mark.asyncio
async def test_parse_document_invalid_file(mock_api_key, tmp_path, mock_parser):
    """Test parsing with invalid file type."""
    with patch(
        "cneutral_doc.api.parser.service.MarkerParser", return_value=mock_parser
    ):
        # Create a text file instead of PDF
        txt_file = tmp_path / "test.txt"
        txt_file.write_text("This is not a PDF")

        mock_file = MagicMock(spec=UploadFile)
        mock_file.filename = txt_file.name
        mock_file.read = AsyncMock(return_value=txt_file.read_bytes())

        with pytest.raises(HTTPException) as exc_info:
            await parse_document(
                request=MagicMock(),
                file=mock_file,
                isolate_tables=True,
                format="markdown",
                api_key=mock_api_key,
            )

        assert exc_info.value.status_code == HTTP_400_BAD_REQUEST
        assert "Invalid file format" in exc_info.value.detail


@pytest.mark.asyncio
async def test_parse_document_parser_error(mock_api_key, mock_pdf_file, mock_parser):
    """Test handling of parser exceptions."""
    with patch(
        "cneutral_doc.api.parser.service.MarkerParser", return_value=mock_parser
    ), patch.object(mock_parser, "parse", side_effect=Exception("Parser error")):

        mock_file = MagicMock(spec=UploadFile)
        mock_file.filename = mock_pdf_file.name
        mock_file.read = AsyncMock(return_value=mock_pdf_file.read_bytes())

        with pytest.raises(HTTPException) as exc_info:
            await parse_document(
                request=MagicMock(),
                file=mock_file,
                isolate_tables=True,
                format="markdown",
                api_key=mock_api_key,
            )

        assert exc_info.value.status_code == HTTP_500_INTERNAL_SERVER_ERROR
        assert "Internal server error during document parsing" in str(
            exc_info.value.detail
        )


@pytest.mark.asyncio
async def test_parse_document_empty_file(mock_api_key, mock_parser):
    """Test parsing with empty file."""
    with patch(
        "cneutral_doc.api.parser.service.MarkerParser", return_value=mock_parser
    ):
        mock_file = MagicMock(spec=UploadFile)
        mock_file.filename = "empty.pdf"
        mock_file.read = AsyncMock(return_value=b"")

        with pytest.raises(HTTPException) as exc_info:
            await parse_document(
                request=MagicMock(),
                file=mock_file,
                isolate_tables=True,
                format="markdown",
                api_key=mock_api_key,
            )

        assert exc_info.value.status_code == HTTP_400_BAD_REQUEST
        assert "Empty file received" in exc_info.value.detail
