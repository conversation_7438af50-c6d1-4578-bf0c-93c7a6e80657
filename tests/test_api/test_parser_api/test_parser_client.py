"""Tests for the CNeutral document parser client module."""

import logging
import os
from unittest.mock import MagicMock, patch

import pytest
import requests
from requests.exceptions import RequestException

from cneutral_doc.api.parser.client import CNeutralParserClient
from cneutral_doc.document.parser.base import Page, ParsedDocument, Table
from cneutral_doc.document.parser.marker import MarkerParser

# Silence client module logger during tests to avoid noise
logging.getLogger("cneutral_doc.api.parser.client").setLevel(logging.CRITICAL)


@pytest.fixture
def mock_pdf_file(tmp_path):
    """Create a mock PDF file for testing."""
    pdf_path = tmp_path / "test.pdf"
    pdf_path.write_bytes(b"%PDF-1.5\nMock PDF content")
    return pdf_path


@pytest.fixture
def mock_parsed_document():
    """Create a mock parsed document for testing."""
    return ParsedDocument(
        name="test_document",
        pages=[
            Page(
                content="Test page content", page_number=1, metadata={"source": "test"}
            ),
            Page(content="Another test page", page_number=2, metadata={}),
        ],
        tables=[Table(content="Test table", index=1)],
        metadata={"title": "Test Document", "pages": 2},
        raw_data={"text": "Raw test content"},
        images=[],
    )


@pytest.fixture
def mock_response():
    """Create a mock response for requests."""
    mock_resp = MagicMock()
    mock_resp.ok = True
    mock_resp.status_code = 200
    mock_resp.json.return_value = {
        "name": "test_document",
        "pages": [
            {
                "content": "Test page content",
                "page_number": 1,
                "metadata": {"source": "test"},
            },
            {"content": "Another test page", "page_number": 2, "metadata": {}},
        ],
        "tables": [{"content": "Test table", "index": 1}],
        "metadata": {"title": "Test Document", "pages": 2},
        "raw_data": {"text": "Raw test content"},
    }
    return mock_resp


def test_client_parse_document_success_as_json(mock_pdf_file, mock_response):
    """Test successful PDF document parsing with the client returning JSON."""
    # Mock the requests.post method
    with patch("requests.post", return_value=mock_response) as mock_post:
        # Create a client instance and call parse_document
        client = CNeutralParserClient(base_url="http://test-url", api_key="test-key")
        result = client.parse_document(
            file_path=mock_pdf_file,
            isolate_tables=False,
            format="markdown",
            as_json=True,
        )

        # Verify requests.post was called with correct parameters
        mock_post.assert_called_once()
        call_args = mock_post.call_args
        assert call_args[0][0] == "http://test-url/parse"
        assert "file" in call_args[1]["files"]
        assert call_args[1]["params"] == {"isolate_tables": False, "format": "markdown"}
        assert call_args[1]["headers"] == {
            "X-API-Key": "test-key",
            "Accept": "application/json",
        }

        # Verify result structure
        assert isinstance(result, dict)
        assert "name" in result
        assert "pages" in result
        assert "tables" in result
        assert "metadata" in result
        assert "raw_data" in result

    # Verify content matches mock document
    assert result["name"] == "test_document"
    assert len(result["pages"]) == 2
    assert result["pages"][0]["content"] == "Test page content"
    assert result["pages"][0]["page_number"] == 1
    assert result["pages"][0]["metadata"] == {"source": "test"}

    assert len(result["tables"]) == 1
    assert result["tables"][0]["content"] == "Test table"
    assert result["tables"][0]["index"] == 1


def test_client_parse_document_as_parsed_document(
    mock_pdf_file, mock_response, mock_parsed_document
):
    """Test successful PDF document parsing with the client returning a ParsedDocument object."""
    # Mock the requests.post method
    with patch("requests.post", return_value=mock_response) as mock_post:
        # Mock the MarkerParser.from_dict method to return our mock parsed document
        with patch.object(
            MarkerParser, "from_dict", return_value=mock_parsed_document
        ) as mock_from_dict:
            # Create a client instance and call parse_document
            client = CNeutralParserClient(
                base_url="http://test-url", api_key="test-key"
            )
            result = client.parse_document(
                file_path=mock_pdf_file,
                isolate_tables=False,
                format="markdown",
                as_json=False,
            )

            # Verify requests.post was called with correct parameters
            mock_post.assert_called_once()
            call_args = mock_post.call_args
            assert call_args[0][0] == "http://test-url/parse"
            assert "file" in call_args[1]["files"]
            assert call_args[1]["params"] == {
                "isolate_tables": False,
                "format": "markdown",
            }

            # Verify MarkerParser.from_dict was called with the response data
            mock_from_dict.assert_called_once_with(mock_response.json())

            # Verify result is a ParsedDocument object and matches our mock
            assert isinstance(result, ParsedDocument)
            assert result is mock_parsed_document


def test_client_with_env_api_key(mock_pdf_file):
    """Test using API key from environment variable."""
    with patch.dict(os.environ, {"CNEUTRAL_API_KEY": "env-api-key"}):
        with patch(
            "cneutral_doc.api.parser.client.load_api_key", return_value="env-api-key"
        ):
            with patch.object(CNeutralParserClient, "parse_document", return_value={}):
                client = CNeutralParserClient()

                # Verify the API key was loaded from environment
                assert "X-API-Key" in client.headers
                assert client.headers["X-API-Key"] == "env-api-key"


def test_client_missing_api_key():
    """Test client initialization with missing API key."""
    with patch.dict(os.environ, {}, clear=True):
        with patch("cneutral_doc.api.parser.client.load_dotenv", return_value=False):
            # For non-localhost URLs, API key is required
            with pytest.raises(ValueError) as exc_info:
                CNeutralParserClient(base_url="https://api.example.com")
            assert "API key is required" in str(exc_info.value)


def test_client_with_explicit_api_key():
    """Test client initialization with explicitly provided API key."""
    # Even with no environment variables, explicit API key should work
    with patch.dict(os.environ, {}, clear=True):
        client = CNeutralParserClient(api_key="explicit-test-key")
        assert client.headers["X-API-Key"] == "explicit-test-key"


def test_client_file_not_found():
    """Test error when file doesn't exist."""
    client = CNeutralParserClient(api_key="test-key")

    with pytest.raises(ValueError) as exc_info:
        client.parse_document(file_path="/nonexistent/test.pdf")

    assert "File not found" in str(exc_info.value)


def test_client_not_pdf(tmp_path):
    """Test error when file is not a PDF."""
    text_file = tmp_path / "test.txt"
    text_file.write_text("Not a PDF")

    client = CNeutralParserClient(api_key="test-key")
    with pytest.raises(ValueError) as exc_info:
        client.parse_document(file_path=text_file)

    assert "Only PDF files are supported" in str(exc_info.value)


def test_client_request_error(mock_pdf_file):
    """Test handling of request errors."""
    with patch.object(
        CNeutralParserClient,
        "parse_document",
        side_effect=RequestException("Network error"),
    ):
        client = CNeutralParserClient(api_key="test-key")

        with pytest.raises(RequestException) as exc_info:
            client.parse_document(file_path=mock_pdf_file)

        assert "Network error" in str(exc_info.value)


def test_client_unexpected_error(mock_pdf_file):
    """Test handling of unexpected errors."""
    with patch.object(requests, "post", side_effect=Exception("Unexpected error")):
        client = CNeutralParserClient(api_key="test-key")

        with pytest.raises(ValueError) as exc_info:
            client.parse_document(file_path=mock_pdf_file)

        assert "Unexpected error processing document" in str(exc_info.value)


def test_client_file_size_limit(tmp_path):
    """Test file size validation."""
    # Create a mock PDF file
    pdf_path = tmp_path / "test.pdf"
    pdf_path.write_bytes(b"%PDF-1.5\nMock PDF content")

    # Mock stat to return a large file size
    with patch("pathlib.Path.stat") as mock_stat:
        # Create a mock object with st_size and st_mode attributes
        mock_stat_result = type(
            "MockStatResult",
            (),
            {
                "st_size": 250 * 1024 * 1024,  # 250MB
                "st_mode": 33188,  # Regular file mode
            },
        )
        mock_stat.return_value = mock_stat_result

        # Test with default limit (200MB)
        client = CNeutralParserClient(api_key="test-key")
        with pytest.raises(ValueError) as exc_info:
            client.parse_document(file_path=pdf_path)

        assert "exceeds maximum allowed size" in str(exc_info.value)
        assert "200 MB" in str(exc_info.value)


def test_client_custom_file_size_limit(mock_pdf_file, mock_response):
    """Test custom file size limit parameter."""
    # Mock the file stat to return a size just under the custom limit
    with patch("pathlib.Path.stat") as mock_stat:
        mock_stat_result = type(
            "MockStatResult",
            (),
            {
                "st_size": 45 * 1024 * 1024,  # 45MB (under 50MB limit)
                "st_mode": 33188,  # Regular file mode
            },
        )
        mock_stat.return_value = mock_stat_result

        # Mock the requests.post method
        with patch("requests.post", return_value=mock_response) as mock_post:
            # Create client and call with custom limit
            client = CNeutralParserClient(api_key="test-key")
            client.parse_document(
                file_path=mock_pdf_file,
                max_file_size_mb=50,  # 50MB limit
            )

            # Verify requests.post was called
            mock_post.assert_called_once()


@pytest.mark.parametrize(
    "format_type,expected_format",
    [
        ("markdown", "markdown"),
        ("text", "text"),
        ("html", "html"),
    ],
)
def test_client_formats(mock_pdf_file, mock_response, format_type, expected_format):
    """Test different output formats."""
    # Mock the requests.post method
    with patch("requests.post", return_value=mock_response) as mock_post:
        # Create client and call with specific format
        client = CNeutralParserClient(api_key="test-key")
        client.parse_document(
            file_path=mock_pdf_file,
            format=format_type,
        )

        # Verify format parameter was passed correctly
        call_args = mock_post.call_args
        assert call_args[1]["params"]["format"] == expected_format


@pytest.mark.parametrize(
    "isolate_tables,expected_isolation",
    [
        (True, True),  # Explicitly enabled
        (False, False),  # Explicitly disabled
        (None, False),  # Default behavior
    ],
)
def test_client_isolate_tables(
    mock_pdf_file, mock_response, isolate_tables, expected_isolation
):
    """Test table isolation parameter with different configurations."""
    # Mock the requests.post method
    with patch("requests.post", return_value=mock_response) as mock_post:
        # Create client and call with specific isolate_tables value
        client = CNeutralParserClient(api_key="test-key")
        kwargs = {"file_path": mock_pdf_file}
        if isolate_tables is not None:
            kwargs["isolate_tables"] = isolate_tables

        client.parse_document(**kwargs)

        # Verify isolate_tables parameter was passed correctly
        call_args = mock_post.call_args
        assert call_args[1]["params"]["isolate_tables"] == expected_isolation
