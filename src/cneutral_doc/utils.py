import logging
import sys
from logging.handlers import R<PERSON>tingFileHandler
from pathlib import Path

import yaml


def get_project_root() -> str:
    """Get the project root directory."""
    return Path(__file__).parent.parent.parent.resolve().as_posix()


def setup_logging(
    module_name: str,
    log_level: int = logging.INFO,
    max_bytes: int = 10 * 1024 * 1024,  # 10MB
    backup_count: int = 5,
    console_output: bool = True,
) -> logging.Logger:
    """Set up logging for a module with rotating file handler.

    Args:
        module_name: Name of the module (usually __name__)
        log_level: Logging level (default: INFO)
        max_bytes: Maximum size of log file before rotation (default: 10MB)
        backup_count: Number of backup files to keep (default: 5)
        console_output: Whether to also log to console (default: True)

    Returns:
        Configured logger instance
    """
    # Get the module's file stem for log filename
    if module_name == "__main__":
        # For scripts run as __main__, use the actual script filename
        script_path = Path(sys.argv[0])
        log_filename = f"{script_path.stem}.log"
    else:
        # Extract the last part of the module name and use as filename
        module_parts = module_name.split(".")
        log_filename = f"{module_parts[-1]}.log"

    # Set up logging directory using get_project_root()
    log_dir = Path(get_project_root()) / "logs"
    log_dir.mkdir(parents=True, exist_ok=True)
    log_path = log_dir / log_filename

    # Create logger
    logger = logging.getLogger(module_name)

    # Avoid adding handlers multiple times
    if logger.handlers:
        return logger

    logger.setLevel(log_level)

    # Create formatter
    formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s - [%(filename)s:%(lineno)d]"
    )

    # Rotating file handler
    file_handler = RotatingFileHandler(
        log_path, maxBytes=max_bytes, backupCount=backup_count, encoding="utf-8"
    )
    file_handler.setFormatter(formatter)
    file_handler.setLevel(log_level)
    logger.addHandler(file_handler)

    # Console handler (optional)
    if console_output:
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        console_handler.setLevel(log_level)
        logger.addHandler(console_handler)

    return logger


def read_config(config_path: str = "config.yaml", config_dir: str = "config") -> dict:
    config_file_path = Path(get_project_root()) / config_dir / config_path
    with open(config_file_path, "r") as f:
        return yaml.safe_load(f)


def get_data_root() -> str:
    return read_config()["paths"]["data_root"]


if __name__ == "__main__":
    print(get_project_root())
    print(read_config())
    print(get_data_root())
