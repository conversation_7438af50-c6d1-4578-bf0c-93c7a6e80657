from pathlib import Path

import yaml


def get_project_root() -> str:
    """Get the project root directory."""
    return Path(__file__).parent.parent.parent.resolve().as_posix()


def read_config(config_path: str = "config.yaml", config_dir: str = "config") -> dict:
    config_file_path = Path(get_project_root()) / config_dir / config_path
    with open(config_file_path, "r") as f:
        return yaml.safe_load(f)


def get_data_root() -> str:
    return read_config()["paths"]["data_root"]


if __name__ == "__main__":
    print(get_project_root())
    print(read_config())
    print(get_data_root())
