"""Base classes for document parsers.

This module provides the base classes and data structures for parsing documents
in various formats. It defines the core interfaces that specific parser
implementations must follow.
"""

from __future__ import annotations

from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

PathLike = Union[str, Path]


@dataclass(frozen=True)
class Page:
    """Represents a parsed page from a document.

    Attributes:
        content: The textual content of the page.
        page_number: The sequential number of the page in the document.
        metadata: Optional metadata associated with the page.
    """

    content: str
    page_number: int
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass(frozen=True)
class Table:
    """Represents a parsed table from a document.

    Attributes:
        content: The textual content of the table.
        index: The sequential index of the table in the document.
    """

    content: str
    index: int


@dataclass(frozen=True)
class ParsedDocument:
    """Container for parsed document data.

    A structured representation of a parsed document, including its pages,
    metadata, and optional components like images and tables.

    Attributes:
        pages: List of parsed pages from the document.
        metadata: Document-level metadata.
        raw_data: Raw parser output for potential further processing.
        images: Optional list of image data dictionaries.
        tables: Optional list of parsed tables.
    """

    name: str
    pages: List[Page]
    metadata: Dict[str, Any]
    raw_data: Any
    images: List[Dict[str, Any]] = field(default_factory=list)
    tables: List[Table] = field(default_factory=list)

    def to_dict(self) -> Dict[str, Any]:
        """Convert the document to a dictionary suitable for JSON serialization.

        This method excludes the images field to make the output suitable for
        JSON serialization.

        Returns:
            Dict[str, Any]: A dictionary representation of the document.
        """
        return {
            "name": self.name,
            "pages": [
                {
                    "content": page.content,
                    "page_number": page.page_number,
                    "metadata": page.metadata,
                }
                for page in self.pages
            ],
            "metadata": self.metadata,
            "raw_data": self.raw_data,
            "tables": [
                {"content": table.content, "index": table.index}
                for table in self.tables
            ],
        }


class DocumentParser(ABC):
    """Abstract base class for document parsers.

    This class defines the interface that all document parsers must implement.
    Specific implementations (like MarkerParser, PyPDFParser) will inherit from this
    and implement the abstract methods.

    Attributes:
        config: Parser configuration parameters.
    """

    def __init__(self, **kwargs: Any) -> None:
        """Initialize parser with optional configuration.

        Args:
            **kwargs: Configuration parameters for the parser.
        """
        self.config: Dict[str, Any] = kwargs

    @abstractmethod
    def parse(
        self, file_path: PathLike, isolate_tables: bool = False
    ) -> ParsedDocument:
        """Parse a document file into a structured format.

        Args:
            file_path: Path to the document file to parse.
            isolate_tables: Whether to isolate tables in the document.

        Returns:
            ParsedDocument containing the structured content.

        Raises:
            FileNotFoundError: If the file doesn't exist.
            ValueError: If the file is not a valid document.
        """

    @abstractmethod
    def is_supported(self, file_path: PathLike) -> bool:
        """Check if the given file is supported by this parser.

        Args:
            file_path: Path to the file to check.

        Returns:
            Boolean indicating whether this parser can handle the file.
        """
