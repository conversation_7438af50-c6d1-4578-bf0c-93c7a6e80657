"""Client for CNeutral document parser API.

This module provides a client interface for the CNeutral document parser API
that can extract structured data from PDF documents.

Usage:
    # As an imported module
    from cneutral_doc.api.parser.client import CNeutralParserClient

    client = CNeutralParserClient(api_key="your-api-key")
    result = client.parse_document(
        file_path="path/to/document.pdf",
        isolate_tables=True,
        format="markdown",
        timeout=900,
        as_json=True  # Return as dictionary instead of ParsedDocument object
    )

    # Or run as a command-line script
    python -m cneutral_doc.api.client \
        --input path/to/document.pdf     # Required: Path to the PDF document
        --output results.json            # Required: Path to save the results
        --api-key your-api-key           # Optional: API key (defaults to env var)
        --api-url http://localhost:8001  # Optional: API endpoint (default: http://localhost:8001)
        --format markdown                # Optional: Output format (default: markdown, options: markdown, text, html)
        --timeout 900                    # Optional: Request timeout in seconds (default: 900)
        --isolate-tables                 # Optional: Extract tables separately (default: disabled)
        --max-file-size 200              # Optional: Maximum file size in MB (default: 200)
        --as-json                        # Optional: Return results as JSON (default: enabled)
"""

import json
import logging
import os
from pathlib import Path
from typing import Any, Dict, Literal, Union

import requests
from dotenv import load_dotenv
from requests.exceptions import RequestException

from cneutral_doc.document.parser.base import ParsedDocument
from cneutral_doc.document.parser.marker import MarkerParser
from cneutral_doc.utils import get_project_root

# Module logger - configuration should be handled by the application
logger = logging.getLogger(__name__)


def load_api_key() -> str:
    """Load API key from environment or .env file.

    Returns:
        str: The API key

    Raises:
        ValueError: If API key is not found in environment or .env file
        RuntimeError: If project root cannot be determined
    """
    api_key = os.environ.get("CNEUTRAL_API_KEY")

    # If not in environment, try loading from .env file
    if not api_key:
        try:
            project_root = get_project_root()
        except Exception as e:
            raise RuntimeError(f"Could not determine project root: {str(e)}")

        env_path = Path(project_root) / ".env"
        if env_path.exists():
            load_dotenv(dotenv_path=env_path)
            api_key = os.environ.get("CNEUTRAL_API_KEY")

    # If still not available, raise error
    if not api_key:
        raise ValueError(
            "API key is required. Set CNEUTRAL_API_KEY environment variable "
            "or add it to your .env file."
        )

    return api_key


# Match supported formats in MarkerParser
FormatType = Literal["markdown", "text", "html"]


class CNeutralParserClient:
    """Client for interacting with the CNeutral document parser API."""

    def __init__(
        self, base_url: str = "http://localhost:8001", api_key: str | None = None
    ):
        """Initialize the API client.

        Args:
            base_url: Base URL of the parser API service
            api_key: API key for authentication (defaults to CNEUTRAL_API_KEY env var if None)

        Raises:
            ValueError: If api_key is None and CNEUTRAL_API_KEY is not set in environment
            RuntimeError: If project root cannot be determined when loading from .env
        """
        if not base_url:
            raise ValueError("Base URL cannot be empty")

        self.base_url = base_url.rstrip("/")

        if not api_key:
            try:
                api_key = load_api_key()
            except (ValueError, RuntimeError) as e:
                logger.error(f"Failed to load API key: {str(e)}")
                raise

        if not api_key:
            raise ValueError("API key is required")

        self.headers = {"X-API-Key": api_key, "Accept": "application/json"}

    def parse_document(
        self,
        file_path: Union[str, Path],
        isolate_tables: bool = False,
        format: FormatType = "markdown",
        timeout: int = 900,  # Default timeout for larger documents
        max_file_size_mb: int = 200,  # Maximum file size in megabytes
        as_json: bool = True,
    ) -> Union[ParsedDocument, Dict[str, Any]]:
        """Send a document to the parser service for processing.

        Args:
            file_path: Path to the document file
            isolate_tables: Whether to extract tables separately
            format: Output format for parsed content ("markdown", "text", or "html")
            timeout: Request timeout in seconds (default: 900 seconds)
            max_file_size_mb: Maximum allowed file size in megabytes (default: 200)
            as_json: If True, returns a dictionary instead of a ParsedDocument object

        Returns:
            If as_json is False (default): ParsedDocument containing structured document data
            If as_json is True: Dict containing parsed document data with keys:
                - pages: List of dicts with page content, number and metadata
                - tables: List of dicts with table content and index
                - metadata: Document metadata
                - raw_data: Raw parsed data
            Note: images field will be None as image processing is not supported in API

        Raises:
            ValueError: If file doesn't exist, is not a PDF, invalid format, or too large
            RequestException: If API request fails
            TypeError: If response data is malformed
        """
        # Convert string path to Path object if needed
        if isinstance(file_path, str):
            try:
                file_path = Path(file_path).resolve()
            except Exception as e:
                error_msg = f"Invalid file path format: {str(e)}"
                logger.error(error_msg)
                raise ValueError(error_msg)

        # Validate file path
        if not file_path.exists():
            error_msg = f"File not found: {file_path}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        if not file_path.is_file():
            error_msg = f"Path is not a file: {file_path}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        if file_path.suffix.lower() != ".pdf":
            error_msg = f"Only PDF files are supported, got: {file_path.suffix}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        # Check file size
        file_size_mb = file_path.stat().st_size / (1024 * 1024)
        if file_size_mb > max_file_size_mb:
            error_msg = f"File size ({file_size_mb:.1f} MB) exceeds maximum allowed size ({max_file_size_mb} MB)"
            logger.error(error_msg)
            raise ValueError(error_msg)

        try:
            # Open and validate file
            with open(file_path, "rb") as f:
                files = {"file": (file_path.name, f, "application/pdf")}
                params = {
                    "isolate_tables": isolate_tables,
                    "format": format,
                }

                try:
                    response = requests.post(
                        f"{self.base_url}/parse",
                        headers=self.headers,
                        files=files,
                        params=params,
                        timeout=timeout,
                    )

                    # Handle specific HTTP error cases
                    if not response.ok:
                        try:
                            error_data = response.json()
                            error_detail = error_data.get("detail", error_data)
                        except ValueError:
                            error_detail = response.text or "Unknown error"

                        if response.status_code == 400:
                            logger.error(f"Bad request: {error_detail}")
                            raise ValueError(f"Bad request: {error_detail}")
                        elif response.status_code == 422:
                            logger.error(f"Validation error: {error_detail}")
                            raise ValueError(f"Validation error: {error_detail}")
                        else:
                            logger.error(f"Request failed: {error_detail}")
                            response.raise_for_status()

                except requests.exceptions.Timeout:
                    logger.error("Request timed out")
                    raise RequestException(f"Request timed out after {timeout} seconds")
                except requests.exceptions.ConnectionError as e:
                    logger.error(f"Connection error: {str(e)}")
                    raise RequestException(f"Failed to connect to server: {str(e)}")

                try:
                    data = response.json()
                except ValueError as e:
                    logger.error(f"Invalid JSON response: {str(e)}")
                    raise TypeError(f"Server returned invalid JSON: {str(e)}")

                # Validate required fields in response
                required_fields = {"name", "pages", "metadata", "raw_data"}
                missing_fields = required_fields - set(data.keys())
                if missing_fields:
                    error_msg = (
                        f"Malformed response: missing required fields {missing_fields}"
                    )
                    logger.error(error_msg)
                    raise TypeError(error_msg)

                try:
                    if as_json:
                        # Return the data as a dictionary
                        # No need to reconstruct the dictionary, just return the API response
                        logger.debug(
                            f"Successfully parsed document as JSON: {file_path}"
                        )
                        return data
                    else:
                        # Use MarkerParser.from_dict to create a ParsedDocument object
                        parsed_doc = MarkerParser.from_dict(data)
                        logger.debug(
                            f"Successfully parsed document as ParsedDocument: {file_path}"
                        )
                        return parsed_doc
                except (KeyError, AttributeError) as e:
                    error_msg = f"Error constructing {'result dictionary' if as_json else 'ParsedDocument'}: {str(e)}"
                    logger.error(error_msg)
                    raise TypeError(error_msg)

        except (ValueError, TypeError, RequestException):
            # Re-raise these exceptions as they are already properly formatted
            raise
        except Exception as e:
            logger.error(f"Unexpected error: {str(e)}")
            raise ValueError(f"Unexpected error processing document: {str(e)}")


if __name__ == "__main__":
    import argparse

    # Configure logging for CLI usage only
    logging.basicConfig(
        level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
    )

    # Parse command line arguments
    parser = argparse.ArgumentParser(
        description="Process a PDF document using the CNeutral parser service"
    )
    parser.add_argument(
        "--input", type=str, required=True, help="Path to the input PDF file"
    )
    parser.add_argument(
        "--output", type=str, required=True, help="Path to save the output JSON file"
    )
    parser.add_argument(
        "--api-key",
        type=str,
        default=None,
        help="API key for authentication (default: CNEUTRAL_API_KEY env var)",
    )
    parser.add_argument(
        "--api-url",
        type=str,
        default="http://localhost:8001",
        help="Base URL of the API service (default: http://localhost:8001)",
    )
    parser.add_argument(
        "--format",
        type=str,
        choices=["markdown", "text", "html"],
        default="markdown",
        help="Output format for parsed content (default: markdown)",
    )
    parser.add_argument(
        "--timeout",
        type=int,
        default=900,
        help="Request timeout in seconds (default: 900)",
    )
    parser.add_argument(
        "--isolate-tables",
        action="store_true",
        dest="isolate_tables",
        default=False,
        help="Enable separate table extraction (default: False)",
    )

    args = parser.parse_args()

    try:
        # Validate input file
        input_path = Path(args.input).resolve()
        if not input_path.exists():
            logger.error(f"Input file not found: {input_path}")
            raise SystemExit(1)
        if not input_path.is_file():
            logger.error(f"Input path is not a file: {input_path}")
            raise SystemExit(1)
        if input_path.suffix.lower() != ".pdf":
            logger.error(f"Only PDF files are supported, got: {input_path.suffix}")
            raise SystemExit(1)

        # Validate output directory
        output_path = Path(args.output).resolve()
        output_dir = output_path.parent
        if not output_dir.exists():
            logger.info(f"Creating output directory: {output_dir}")
            output_dir.mkdir(parents=True)
        if output_path.exists():
            logger.warning(f"Output file will be overwritten: {output_path}")

        # Parse the document using the client interface
        logger.info(f"Processing document: {input_path}")
        try:
            client = CNeutralParserClient(base_url=args.api_url, api_key=args.api_key)
            result = client.parse_document(
                file_path=input_path,
                isolate_tables=args.isolate_tables,
                format=args.format,
                timeout=args.timeout,
                as_json=True,
            )
        except Exception as e:
            logger.error(f"Error during document parsing: {str(e)}")
            raise

        # Save to JSON file
        logger.info(f"Saving results to: {output_path}")
        with open(output_path, "w", encoding="utf-8") as f:
            json.dump(result, f, indent=2, ensure_ascii=False)

        logger.info(f"Results successfully saved to {output_path}")
        raise SystemExit(0)

    except ValueError as e:
        logger.error(str(e))
        raise SystemExit(1)
    except RequestException as e:
        logger.error(f"API request failed: {str(e)}")
        raise SystemExit(1)
    except Exception as e:
        logger.error(f"Error processing document: {str(e)}")
        raise SystemExit(1)
