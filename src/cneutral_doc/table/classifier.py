"""Implementation of the table classifier using LangChain with structured output."""

import json
import logging
import math
import os
import re
from abc import abstractmethod
from collections import Counter
from typing import Any, Dict, List, Optional, Tuple, Union, cast

from langchain_core.exceptions import LangChainException, OutputParserException
from langchain_core.prompts import ChatPromptTemplate
from langchain_google_genai import ChatGoogleGenerativeAI
from pydantic import BaseModel, Field, SecretStr, ValidationError

from cneutral_doc.table.base import BaseTableClassifier, TableClassificationResult

logger = logging.getLogger(__name__)


class ESGDefinition:
    ENVIRONMENT_DEFINITION = (
        "This category encompasses tables primarily containing information related to the natural environment "
        "and a company's impact on it, as well as their dependence on natural resources. It focuses on resource "
        "use, resource availability and biodiversity as impacted by the corporation and its operations. \n"
        "Focus areas: \n"
        " - Resource Use: Energy consumption (electricity, fuels, water), raw material sourcing, waste generation, "
        "recycling rates, water usage, land use. \n"
        " - Emissions: Greenhouse gas (GHG) emissions (Scope 1, Scope 2, and Scope 3), air pollutants (NOx, SOx, PM), water "
        "discharge pollutants. \n"
        " - Environmental Impacts: Deforestation, biodiversity loss, pollution incidents, spills, habitat "
        "destruction, risks of stranded assets. \n"
        " - Climate Change: Climate-related risks and opportunities, resilience to extreme weather events, carbon "
        "footprint reduction initiatives, investments in renewable energy, transition risks, adaptation plans. \n"
        " - Circular Economy: Product design for recyclability, material recovery, waste reduction, extended "
        "producer responsibility."
    )

    SOCIAL_DEFINITION = (
        "This category covers tables primarily concerning a company's relationships with and impact on its "
        "workforce, customers, communities, and other stakeholders. It focuses on human capital, labor practices, "
        "diversity, health and safety, product responsibility, and community engagement. \n"
        "Focus areas: \n"
        " - Workforce: Employee demographics, diversity and inclusion metrics, employee training hours, "
        "compensation and benefits, employee turnover rates, unionization, workplace safety statistics (e.g., "
        "accident rates, fatalities). \n"
        " - Human Rights: Forced labor, child labor, modern slavery risks, human rights due diligence processes, "
        "grievance mechanisms. \n"
        " - Product Responsibility: Product safety, product quality, labeling and marketing practices, data "
        "privacy, customer satisfaction, responsible innovation. \n"
        " - Community Engagement: Community investment programs, philanthropic contributions, local job creation, "
        "stakeholder engagement processes, impact assessments. \n"
        " - Supply Chain: Labor standards in the supply chain, supplier diversity, responsible sourcing practices."
    )

    GOVERNANCE_DEFINITION = (
        "This category encompasses tables primarily concerning a company's leadership structure, ethical "
        "practices, risk management, and overall governance framework. It focuses on transparency, accountability, "
        "and the company's ability to make sound decisions in the long-term interests of stakeholders. \n"
        "Focus areas: \n"
        " - Board Composition: Board diversity, independence, expertise, tenure. \n"
        " - Executive Compensation: CEO pay ratio, performance-based compensation metrics, alignment with "
        "sustainability goals. \n"
        " - Ethics and Compliance: Code of conduct, anti-corruption policies, whistleblowing mechanisms, legal and "
        "regulatory compliance. \n"
        " - Risk Management: Identification and management of ESG-related risks, internal controls, business "
        "continuity planning. \n"
        " - Lobbying and Political Contributions: Transparency in lobbying activities, political spending policies, "
        "alignment with company values. \n"
        " - Shareholder Rights: Shareholder voting rights, proxy access, engagement with shareholders on ESG issues."
    )


class ESGScores(BaseModel):
    """ESG classification scores and reasoning.

    Scores must sum to exactly 1.0. The highest score should be for the category
    that the table most closely aligns with. A category that the table does not
    align with should have a confidence score of 0.0.
    """

    reasoning: str = Field(
        ...,
        description="Brief explanation for the classification based on the table content",
    )
    E: float = Field(..., description="Environmental score between 0.0 and 1.0")
    S: float = Field(..., description="Social score between 0.0 and 1.0")
    G: float = Field(..., description="Governance score between 0.0 and 1.0")
    O: float = Field(
        ...,
        description="Other score between 0.0 and 1.0, for content like table of contents, \
GRI standard disclosure reference tables, or content that doesn't fit clearly into E, S, or G categories",
    )


class LLMTableClassifier(BaseTableClassifier):
    """
    Classifier for markdown tables using LangChain to determine
    ESG (Environmental, Social, Governance) confidence scores.
    """

    def __init__(
        self, model_name: str, temp: float, api_key: str, *args, **kwargs
    ) -> None:

        self.model_name = model_name
        self.temp = temp
        self.api_key = api_key
        self.chain = self._create_prompt_template() | self._setup_chat_model()

    def _create_prompt_template(self) -> ChatPromptTemplate:
        """Create a prompt template for the LLM.

        Returns:
            ChatPromptTemplate: A configured prompt template for the classification task.
        """
        template = """Analyze the following markdown table and provide confidence scores for each category (E, S, G, O) that SUM TO EXACTLY 1.0 based strictly on the definitions below. Highest confidence score should be for the category that the table most closely aligns with. A category that the table does not align with should have a confidence score of 0.0.

        DEFINITIONS:

        ENVIRONMENTAL (E):
        {environment_definition}

        SOCIAL (S):
        {social_definition}

        GOVERNANCE (G):
        {governance_definition}

        OTHER (O):
        This category includes content, like table of contents, GRI standard disclosure reference tables, GRI index tables, tables data that cannot be plotted,or tables that does not fit clearly into ENVIRONMENTAL, SOCIAL, or GOVERNANCE categories.

        Now, classify the following markdown table into one of the categories based on the definitions above.
        
        TABLE:
        ```
        {markdown_table}
        ```

        INSTRUCTIONS:
        1. Carefully analyze the table content based on the definitions.
        2. Provide confidence scores for each category (E, S, G, O) that SUM TO EXACTLY 1.0"""

        return ChatPromptTemplate.from_template(template)

    @abstractmethod
    def _setup_chat_model(self) -> Any:
        """Set up and configure the chat model for classification.

        Returns:
            Any: Configured chat model instance
        """
        pass

    def _is_likely_number(self, cell_content: str) -> bool:
        """Checks if a string is likely a numerical value."""
        # Remove common currency symbols, percentages, and thousands separators
        cleaned_content = re.sub(r"[$,€£%]", "", cell_content).replace(",", "")
        # Allow for optional leading +/- and potential decimal point
        return re.match(r"^\s*[-+]?(\d+(\.\d*)?|\.\d+)\s*$", cleaned_content) is not None

    def validate_table(self, markdown_table: str) -> Dict[str, Any]:
        """
        Validate if the markdown string represents a plausible table using a scoring system.

        Args:
            markdown_table: String containing markdown table

        Returns:
            Dict[str, Any]: Dictionary containing validation results:
                - 'is_valid' (bool): True if the table score meets the threshold, False otherwise
                - 'error_message' (Optional[str]): Description of validation issues if score is low, None otherwise
                - 'dimensions' (Tuple[int, int]): Estimated (data_rows, cols) if structure is discernible
                - 'numerical_density' (float): Proportion of data cells containing likely numerical values
                - 'validation_score' (float): Score between 0.0 and 1.0 indicating table plausibility

        Raises:
            ValueError: If input table is None or empty
            TypeError: If input table is not a string
        """
        logger.debug("Starting table validation")

        # --- Initial Checks ---
        if markdown_table is None:
            raise ValueError("Input table cannot be None")
        if not isinstance(markdown_table, str):
            raise TypeError(f"Input table must be string, got {type(markdown_table)}")
        if not markdown_table.strip():
            raise ValueError("Input table cannot be empty")

        # --- Scoring Initialization ---
        score = 1.0  # start with a perfect score (0.0 to 1.0 scale)
        issues: List[str] = []
        final_rows, final_cols = 0, 0
        numerical_density = 0.0

        # --- Preprocessing ---
        try:
            lines = [line.strip() for line in markdown_table.strip().split("\n")]
            lines = [line for line in lines if line] # remove empty lines
            if len(lines) < 2: # need at least header/separator OR header/data OR two data rows
                issues.append(f"Insufficient lines ({len(lines)}) for a meaningful table.")
                score *= 0.1 # heavy penalty
                lines = [] # avoid processing further if too short

        except Exception as e:
            logger.error(f"Failed during line processing: {e}")
            raise RuntimeError(f"Failed to process table lines: {str(e)}")

        if not lines:
             return {
                "is_valid": False,
                "error_message": "; ".join(issues) if issues else "Table too short or preprocessing failed.",
                "dimensions": (0, 0),
                "numerical_density": 0.0,
                "validation_score": score,
             }

        # --- Feature Extraction and Scoring ---
        pipe_counts: List[int] = []
        row_cell_contents: List[List[str]] = []
        separator_line_index = -1
        has_consistent_pipes = False

        for i, line in enumerate(lines):
            # basic structure check: presence of pipes
            if '|' not in line:
                 # allow some non-pipe lines, maybe headers/footers outside strict md format
                 continue # skip lines without pipes for column counting

            pipe_counts.append(line.count('|'))
            # extract cells between pipes
            cells = [cell.strip() for cell in line.split('|')]
            # handle leading/trailing empty strings from split if line starts/ends with |
            if line.startswith('|'): cells = cells[1:]
            if line.endswith('|'): cells = cells[:-1]
            row_cell_contents.append(cells)

            # separator line detection (heuristic)
            # look for line like |---|:---|---| or ---|--- etc. usually 2nd line.
            if i == 1 and len(lines) > 1 and re.match(r"^[|\s]*:?---+:?[|\s:]*$", line.replace(" ", "")):
                 separator_line_index = i

        # --- Score based on Structural Consistency ---
        if not pipe_counts:
            issues.append("No lines with pipe delimiters found.")
            score *= 0.1
        else:
            pipe_count_counter = Counter(pipe_counts)
            most_common_pipe_count, num_most_common = pipe_count_counter.most_common(1)[0]

            # calculate consistency: ratio of lines matching the dominant pipe count
            consistency_ratio = num_most_common / len(pipe_counts)
            if consistency_ratio < 0.7:
                 issues.append(f"Inconsistent pipe count ({consistency_ratio:.2f} ratio for dominant count {most_common_pipe_count}).")
                 score *= (0.5 + 0.5 * consistency_ratio) # penalize based on inconsistency severity
            else:
                 has_consistent_pipes = True # assume structure is likely tabular

            # estimate columns based on dominant pipe count (cells = pipes - 1 usually)
            # or better, use the length of cell lists
            col_counts = [len(row) for row in row_cell_contents]
            if col_counts:
                col_count_counter = Counter(col_counts)
                final_cols, num_final_cols = col_count_counter.most_common(1)[0]
                col_consistency_ratio = num_final_cols / len(col_counts)
                if col_consistency_ratio < 0.8: # slightly stricter for cell counts
                    issues.append(f"Inconsistent cell count ({col_consistency_ratio:.2f} ratio for dominant count {final_cols}).")
                    score *= (0.6 + 0.4 * col_consistency_ratio)
                if final_cols == 0:
                    issues.append("Detected 0 columns consistently.")
                    score *= 0.2

        # --- Score based on Separator ---
        if separator_line_index == -1:
            issues.append("No clear markdown separator line (e.g., |---|---|) detected.")
            score *= 0.8 # moderate penalty, table might still be valid without it
        elif has_consistent_pipes and final_cols > 0:
            # check if separator structure matches column count
            sep_line = lines[separator_line_index]
            # count '---' segments roughly
            sep_segments = len(re.findall(r":?-+:?", sep_line))
            if sep_segments != final_cols:
                issues.append(f"Separator segments ({sep_segments}) mismatch dominant column count ({final_cols}).")
                score *= 0.9 # minor penalty

        # --- Calculate Dimensions and Numerical Density (if structure seems plausible) ---
        data_rows_content: List[List[str]] = []
        if final_cols > 0 and score > 0.3: # only proceed if basic structure exists
            start_row = 0
            if separator_line_index != -1:
                start_row = separator_line_index + 1
            elif len(lines) > 1: # assume first line might be header if no separator
                start_row = 1

            # collect potential data cells (filtering by consistent column count helps)
            potential_data_lines = lines[start_row:]
            for line in potential_data_lines:
                 if '|' in line: # consider only lines with pipes for data cells
                    cells = [cell.strip() for cell in line.split('|')]
                    if line.startswith('|'): cells = cells[1:]
                    if line.endswith('|'): cells = cells[:-1]
                    # only include rows matching the dominant column count for density calculation
                    if len(cells) == final_cols:
                        data_rows_content.append(cells)


            final_rows = len(data_rows_content)

            # calculate numerical density based on *data* rows
            total_data_cells = 0
            numerical_cells = 0
            if final_rows > 0 and final_cols > 0:
                for row in data_rows_content:
                    for cell in row:
                        if cell: # ignore empty cells for density calculation
                           total_data_cells += 1
                           if self._is_likely_number(cell):
                               numerical_cells += 1

                if total_data_cells > 0:
                    numerical_density = numerical_cells / total_data_cells
                    logger.debug(f"Calculated numerical density: {numerical_density:.3f} ({numerical_cells}/{total_data_cells})")
                else:
                    logger.debug("No non-empty data cells found for density calculation.")
                    numerical_density = 0.0
            else:
                 logger.debug("Skipping numerical density calc due to zero rows/cols.")


            # --- Score based on Dimensions and Content ---
            if final_rows < 1:
                 issues.append("Detected 0 data rows.")
                 score *= 0.5 # penalize tables with no data rows
            if final_cols < 2:
                 issues.append("Detected less than 2 columns.")
                 score *= 0.7 # penalize very narrow tables

            # reward tables with *some* numerical content, penalize if *only* non-numeric and small
            if numerical_density > 0.1:
                score = min(1.0, score * 1.1) # slight boost for having numbers
            elif final_rows < 5 and final_cols < 5: # if small and no numbers
                 issues.append("Table is small and has low numerical density.")
                 score *= 0.85 # minor penalty, could be valid text table


        # --- Final Decision ---
        validation_threshold = 0.6 # adjust this threshold based on desired strictness
        is_valid = score >= validation_threshold

        error_message = None
        if not is_valid:
             # prepend the final score to the error message for context
             issues.insert(0, f"Validation score ({score:.2f}) below threshold ({validation_threshold:.2f})")
             error_message = "; ".join(issues)
             logger.warning(f"Table validation failed: {error_message}")
        else:
             logger.info(f"Table validation passed with score {score:.2f}")


        result = {
            "is_valid": is_valid,
            "error_message": error_message,
            "dimensions": (final_rows, final_cols),
            "numerical_density": numerical_density,
            "validation_score": score,
        }
        logger.debug(f"Validation result: {result}")
        return result


    def classify_table(self, markdown_table: str) -> TableClassificationResult:
        """
        Classify a markdown table into ESG categories using a LangChain chain
        with structured output. Uses flexible validation.

        Args:
            markdown_table: String containing markdown table.

        Returns:
            TableClassificationResult with confidence scores and metadata.
            Handles validation and classification errors gracefully.
        """
        logger.info("Starting table classification process.")
        logger.debug(
            f"Input markdown table (first 200 chars):\n{markdown_table[:200]}..."
        )

        validation_score = 0.0 # Initialize validation score

        try:
            val_result = self.validate_table(markdown_table)
            is_valid = val_result["is_valid"]
            validation_message = val_result["error_message"]
            numerical_density = val_result["numerical_density"]
            rows, cols = val_result["dimensions"]
            validation_score = val_result.get("validation_score", 0.0) # Get score from result
            logger.debug(f"Table validation result: {val_result}")
        except Exception as e:
            logger.exception("Critical error during table validation step.")
            # Ensure validation_message reflects the exception
            validation_message = f"Validation exception: {str(e)}"
            is_valid = False
            numerical_density = 0.0
            rows, cols = 0, 0

            # Return early as validation itself failed critically
            return TableClassificationResult(
                is_valid_table=False,
                validation_message=validation_message,
                rows=rows,
                cols=cols,
                numerical_density=numerical_density,
                reasoning=None,
                e_score=0.0,
                s_score=0.0,
                g_score=0.0,
                other_score=1.0, # Default to 'Other' if validation fails
                validation_score=validation_score # Include score even on exception
            )

        if not is_valid:
            logger.warning(
                f"Table is not valid according to validation rules: {validation_message}"
            )
            # Return classification result indicating invalid table
            return TableClassificationResult(
                is_valid_table=False,
                validation_message=validation_message,
                rows=rows,
                cols=cols,
                numerical_density=numerical_density,
                reasoning=None, # No classification attempted
                e_score=0.0,
                s_score=0.0,
                g_score=0.0,
                other_score=1.0, # Default to 'Other' for invalid tables
                validation_score=validation_score # Include the score
            )

        # --- Proceed with Classification for Valid Tables ---
        input_data = {
            "markdown_table": markdown_table,
            "environment_definition": ESGDefinition.ENVIRONMENT_DEFINITION,
            "social_definition": ESGDefinition.SOCIAL_DEFINITION,
            "governance_definition": ESGDefinition.GOVERNANCE_DEFINITION,
        }
        logger.debug("Prepared input data for LLM classification chain.")

        classification_error_msg: Optional[str] = None
        try:
            logger.info("Invoking LLM chain for ESG classification...")

            structured_result = cast(ESGScores, self.chain.invoke(input_data))
            logger.info("LLM chain invocation successful.")
            logger.debug(f"LLM structured output: {structured_result}")

            scores: Dict[str, float] = {
                "E": structured_result.E,
                "S": structured_result.S,
                "G": structured_result.G,
                "O": structured_result.O,
            }
            reasoning = structured_result.reasoning

            total = sum(scores.values())
            # Use a slightly more tolerant check for floating point issues
            if abs(total - 1.0) > 1e-4: # Increased tolerance
                logger.warning(
                    f"LLM scores sum to {total:.4f}, attempting normalization."
                )
                # Ensure scores are non-negative before normalization
                scores = {k: max(0.0, v) for k, v in scores.items()}
                total = sum(scores.values()) # Recalculate total after clamping negatives

                if total <= 1e-6: # Check if total is effectively zero
                    logger.error(
                        "Cannot normalize scores summing to zero or less. Classification failed."
                    )
                    classification_error_msg = (
                        "Classification error: Scores summed to zero after clamping negatives."
                    )
                    # Fallback scores
                    scores = {"E": 0.0, "S": 0.0, "G": 0.0, "O": 1.0}
                else:
                    # Normalize valid scores
                    scores = {k: v / total for k, v in scores.items()}
                    # Ensure sum is exactly 1 after normalization due to potential float issues
                    final_total = sum(scores.values())
                    if abs(final_total - 1.0) > 1e-6:
                         # If still not 1, distribute remainder to largest category (or 'O')
                         diff = 1.0 - final_total
                         max_cat = max(scores, key=scores.get)
                         scores[max_cat] += diff
                         logger.warning(f"Post-normalization adjustment applied. Scores sum to {sum(scores.values()):.6f}")

                    logger.info(
                        f"Normalized scores: E={scores['E']:.2f}, S={scores['S']:.2f}, G={scores['G']:.2f}, O={scores['O']:.2f}"
                    )

            # Check again if an error occurred during normalization
            if classification_error_msg is None:
                final_result = TableClassificationResult(
                    e_score=scores["E"],
                    s_score=scores["S"],
                    g_score=scores["G"],
                    other_score=scores["O"],
                    numerical_density=numerical_density,
                    rows=rows,
                    cols=cols,
                    is_valid_table=True,
                    reasoning=reasoning,
                    validation_message=None, # Clear validation message if valid and classified
                    validation_score=validation_score # Include the validation score
                )
                logger.info("Table classification successful.")
                logger.debug(f"Final classification result: {final_result}")
                return final_result

        # --- Handle Classification Errors ---
        except OutputParserException as e:
            logger.error(f"LLM output failed structured parsing: {e}", exc_info=True)
            classification_error_msg = f"LLM output parsing failed: {e}"
        except LangChainException as e:
            logger.error(f"LangChain invocation failed: {e}", exc_info=True)
            classification_error_msg = f"LangChain classification failed: {e}"
        except ValidationError as e:
            logger.error(
                f"LLM output failed Pydantic validation (unexpected): {e}",
                exc_info=True,
            )
            classification_error_msg = f"LLM output validation failed: {e}"
        except Exception as e:
            logger.exception("An unexpected error occurred during LLM classification.")
            classification_error_msg = f"Unexpected classification error: {e}"

        # --- Return Fallback Result on Classification Failure ---
        logger.warning(
            f"Classification failed ({classification_error_msg}), returning fallback result for a valid table."
        )
        return TableClassificationResult(
            e_score=0.0,
            s_score=0.0,
            g_score=0.0,
            other_score=1.0, # Default to 'Other' on classification error
            numerical_density=numerical_density,
            rows=rows,
            cols=cols,
            is_valid_table=True, # Table was valid, but classification failed
            validation_message=classification_error_msg, # Pass the classification error here
            reasoning="Classification failed.", # Add reasoning for failure
            validation_score=validation_score # Include the validation score
        )


class GeminiTableClassifier(LLMTableClassifier):
    """
    Classifier for markdown tables using LangChain with Google's Gemini model to determine
    ESG (Environmental, Social, Governance) confidence scores.
    """

    def __init__(
        self,
        model_name: str = "gemini-2.0-flash",
        temp: float = 0.1,
        api_key: Optional[str] = None,
        *args,
        **kwargs,
    ) -> None:
        """
        Initialize the GeminiTableClassifier.

        Args:
            model_name: Name of the Gemini model to use. Defaults to 'gemini-1.5-pro'.
            temp: Temperature for model generation. Lower values make output more deterministic.
            api_key: Google API key. If None, will use GOOGLE_API_KEY from environment.
            *args: Additional positional arguments to pass to the parent class.
            **kwargs: Additional keyword arguments to pass to the parent class.
        """
        # if api_key is None, it will be read from environment variable GOOGLE_API_KEY
        api_key_str = (
            api_key if api_key is not None else os.environ.get("GOOGLE_API_KEY_2", "")
        )
        super().__init__(
            model_name=model_name, temp=temp, api_key=api_key_str, *args, **kwargs
        )

    def _setup_chat_model(self) -> Any:
        """
        Set up and configure the Google Gemini chat model for classification.

        Returns:
            Any: Configured ChatGoogleGenerativeAI model with structured output parser.
        """

        logger.debug(f"Setting up Google Gemini model: {self.model_name}")

        # initialize the Google Gemini model
        model = ChatGoogleGenerativeAI(
            model=self.model_name,
            temperature=self.temp,
            api_key=SecretStr(self.api_key) if self.api_key else None,
        )

        # create a model with structured output using the ESGScores Pydantic model
        structured_model = model.with_structured_output(ESGScores)

        logger.debug("Google Gemini model setup complete")
        return structured_model
