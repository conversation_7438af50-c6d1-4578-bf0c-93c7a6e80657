{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from cneutral_doc.api.parser.client import CNeutralParserClient\n", "from cneutral_doc.utils import get_project_root\n", "\n", "%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["ERROR:cneutral_doc.api.parser.client:Connection error: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Error parsing document: Failed to connect to server: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))\n"]}], "source": ["client = CNeutralParserClient()\n", "\n", "small_pdf = f\"{get_project_root()}/tests/data/pdfs/mock_pdf.pdf\"\n", "large_pdf = (\n", "    f\"{get_project_root()}/tests/data/pdfs/Apple_Environmental_Progress_Report_2024.pdf\"\n", ")\n", "\n", "try:\n", "    result = client.parse_document(file_path=large_pdf, isolate_tables=True)\n", "    if isinstance(result, dict):\n", "        print(len(result[\"pages\"]))\n", "        print(len(result[\"tables\"]))\n", "except Exception as e:\n", "    print(f\"Error parsing document: {str(e)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 2}