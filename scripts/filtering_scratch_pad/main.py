import json
import os
import psycopg2
import pandas as pd

from dotenv import load_dotenv

from cneutral_doc.document.parser.marker import MarkerParser
from cneutral_doc.table.filter import TableFilter
from cneutral_doc.table.visualizer import GeminiTableVisualizer
from cneutral_doc.utils import get_project_root

load_dotenv()

PROJECT_ROOT = get_project_root()

# define path of pdf
pdf_path = (
    f"{PROJECT_ROOT}/tests/data/pdfs/annual-review-2024-en.pdf"
)

# parse the pdf
print("Parsing the PDF...")
parser = MarkerParser()
parsed_document = parser.parse(pdf_path, isolate_tables=True)

# save the parsed document (optional)
print("Saving the parsed document...")
with open(f"{PROJECT_ROOT}/tests/data/filtering/apple.json", "w") as f:
    json.dump(parsed_document.to_dict(), f)

# # load the parsed document if previously saved (optional)
# with open(f"{PROJECT_ROOT}/tests/data/filtering/apple.json", 'r') as f:
#     parsed_document = MarkerParser.from_dict(json.load(f))

# filter the tables
print("Filtering the tables...")
table_filter = TableFilter(api_key=os.getenv("GOOGLE_API_KEY_2"))
all_tables = table_filter.filter_tables(parsed_document, k=1)

# save the filtered tables (optional)
print("Saving the filtered tables...")
with open(f'{PROJECT_ROOT}/tests/data/filtering/all_tables.json', 'w') as f:
    json.dump(all_tables, f)

# get the E, S, G tables safely
print("Getting the E, S, G tables...")
e_table = all_tables['E'][0]['content'] if all_tables['E'] else "No environmental tables found"
s_table = all_tables['S'][0]['content'] if all_tables['S'] else "No social tables found"
g_table = all_tables['G'][0]['content'] if all_tables['G'] else "No governance tables found"

# save the tables to files (optional)
print("Saving the tables to files...")
with open(f'{PROJECT_ROOT}/tests/data/filtering/e_table.txt', 'w') as f:
    f.write(e_table)
with open(f'{PROJECT_ROOT}/tests/data/filtering/s_table.txt', 'w') as f:
    f.write(s_table)
with open(f'{PROJECT_ROOT}/tests/data/filtering/g_table.txt', 'w') as f:
    f.write(g_table)

# # load the tables from files (optional)
# with open(f'{PROJECT_ROOT}/tests/data/filtering/e_table.txt', 'r') as f:
#     e_table = f.read()
# with open(f'{PROJECT_ROOT}/tests/data/filtering/s_table.txt', 'r') as f:
#     s_table = f.read()
# with open(f'{PROJECT_ROOT}/tests/data/filtering/g_table.txt', 'r') as f:
#     g_table = f.read()

