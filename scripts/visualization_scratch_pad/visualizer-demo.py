# #!/usr/bin/env python3

# """Demo script for visualizing tables using the GeminiTableVisualizer.

# This script demonstrates how to parse a PDF, filter tables by ESG categories,
# and generate visualizations using the GeminiTableVisualizer.
# """

# import os
# import json
# import logging
# from typing import Dict, List, Optional, Any

# # Configure logging
# logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
# logger = logging.getLogger(__name__)

# # Import required modules
# from dotenv import load_dotenv
# from cneutral_doc.utils import get_project_root
# from cneutral_doc.document.parser.marker import MarkerParser
# from cneutral_doc.table.filter import TableFilter
# from cneutral_doc.table.visualizer import GeminiTableVisualizer

# # Optional database imports (uncomment if you need database functionality)
# import psycopg2  # Comment this out if psycopg2 is not installed
# # import django
# # import sys
# # os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
# # django.setup()
# # from api.models import TaxValuableTable, TaxDoc

# # Load environment variables
# load_dotenv()
# PROJECT_ROOT = get_project_root()
# SAVE_ROOT = os.path.join(PROJECT_ROOT, "tests", "data", "visualization")

# # Ensure save directory exists
# os.makedirs(SAVE_ROOT, exist_ok=True)


# def parse_pdf(pdf_path: str) -> Any:
#     """Parse a PDF document and extract tables.

#     Args:
#         pdf_path: Path to the PDF file

#     Returns:
#         Parsed document object

#     Raises:
#         FileNotFoundError: If the PDF file does not exist
#         RuntimeError: If there's an error parsing the PDF
#     """
#     if not os.path.exists(pdf_path):
#         raise FileNotFoundError(f"PDF file not found: {pdf_path}")

#     logger.info("Parsing the PDF: %s", pdf_path)
#     try:
#         parser = MarkerParser()
#         parsed_document = parser.parse(pdf_path, isolate_tables=True)
#         return parsed_document
#     except Exception as e:
#         logger.error("Failed to parse PDF: %s", str(e))
#         raise RuntimeError(f"Error parsing PDF: {str(e)}") from e


# def save_document(parsed_document: Any, save_path: str) -> None:
#     """Save the parsed document to a file.

#     Args:
#         parsed_document: The parsed document object
#         save_path: Path to save the document
#     """
#     logger.info("Saving the parsed document to: %s", save_path)
#     try:
#         with open(save_path, 'w') as f:
#             json.dump(parsed_document.to_dict(), f)
#         logger.info("Document saved successfully")
#     except Exception as e:
#         logger.error("Failed to save document: %s", str(e))


# def filter_tables(parsed_document: Any, api_key: Optional[str] = None, k: int = 1) -> Dict:
#     """Filter tables by ESG category.

#     Args:
#         parsed_document: The parsed document object
#         api_key: API key for the table filter (uses environment variable if None)
#         k: Number of tables to return for each category
#         save_all: Whether to save all tables

#     Returns:
#         Dictionary of filtered tables by ESG category

#     Raises:
#         RuntimeError: If there's an error filtering tables
#     """
#     logger.info("Filtering tables...")
#     try:
#         # Use environment variable if api_key is None
#         api_key = api_key or os.getenv("GOOGLE_API_KEY")
#         if not api_key:
#             raise ValueError("API key must be provided either as a parameter or through environment variables")

#         table_filter = TableFilter(api_key=api_key)
#         all_tables = table_filter.filter_tables(parsed_document, k=k)
#         return all_tables
#     except Exception as e:
#         logger.error("Failed to filter tables: %s", str(e))
#         raise RuntimeError(f"Error filtering tables: {str(e)}") from e


# def save_tables_to_files(tables: Dict, save_path_prefix: str) -> Dict[str, str]:
#     """Save tables to files and return their contents.

#     Args:
#         tables: Dictionary of tables by category
#         save_path_prefix: Prefix for save paths

#     Returns:
#         Dictionary of table contents by category
#     """
#     logger.info("Saving tables to files...")
#     table_contents = {}

#     for category in ['E', 'S', 'G']:
#         if tables.get(category) and len(tables[category]) > 0:
#             content = tables[category][0]['content']
#         else:
#             content = f"No {category.lower()} tables found"

#         table_contents[category] = content

#         # Save to file
#         save_path = f"{save_path_prefix}/{category.lower()}_table.txt"
#         try:
#             with open(save_path, 'w') as f:
#                 f.write(content)
#             logger.info(f"Saved {category} table to {save_path}")
#         except Exception as e:
#             logger.error(f"Failed to save {category} table: {str(e)}")

#     return table_contents


# def visualize_tables(table_contents: Dict[str, str], api_key: Optional[str] = None) -> Dict[str, str]:
#     """Generate visualizations for tables.

#     Args:
#         table_contents: Dictionary of table contents by category
#         api_key: API key for the visualizer (uses environment variable if None)

#     Returns:
#         Dictionary of visualization code by category
#     """
#     logger.info("Generating visualizations...")
#     visualizations = {}

#     try:
#         # Use environment variable if api_key is None
#         api_key = api_key or os.getenv("GOOGLE_API_KEY")
#         if not api_key:
#             raise ValueError("API key must be provided either as a parameter or through environment variables")

#         visualizer = GeminiTableVisualizer(api_key=api_key)

#         for category, content in table_contents.items():
#             if content and not content.startswith("No"):
#                 try:
#                     visualizations[category] = visualizer.visualize_table(content, category)
#                     logger.info(f"Generated visualization for {category} table")
#                 except Exception as e:
#                     logger.error(f"Failed to visualize {category} table: {str(e)}")
#                     visualizations[category] = ""
#             else:
#                 visualizations[category] = ""

#         return visualizations
#     except Exception as e:
#         logger.error(f"Failed to initialize visualizer: {str(e)}")
#         return {category: "" for category in table_contents.keys()}


# def save_visualizations(visualizations: Dict[str, str], save_path_prefix: str) -> None:
#     """Save visualizations to files.

#     Args:
#         visualizations: Dictionary of visualization code by category
#         save_path_prefix: Prefix for save paths
#     """
#     logger.info("Saving visualizations to files...")

#     for category, code in visualizations.items():
#         if not code:
#             continue

#         save_path = f"{save_path_prefix}/{category.lower()}_visualized.jsx"
#         try:
#             with open(save_path, 'w') as f:
#                 f.write(code)
#             logger.info(f"Saved {category} visualization to {save_path}")
#         except Exception as e:
#             logger.error(f"Failed to save {category} visualization: {str(e)}")


# # Database functions (commented out by default)
# def get_doc_by_hash(conn, doc_hash: str) -> Optional[str]:
#     """Retrieve document by hash.

#     Args:
#         conn: Database connection
#         doc_hash: Document hash

#     Returns:
#         Document hash if found, None otherwise
#     """
#     try:
#         with conn.cursor() as cur:
#             cur.execute("SELECT doc_hash FROM tax_doc WHERE doc_hash = %s", (doc_hash,))
#             result = cur.fetchone()
#             return result[0] if result else None
#     except Exception as e:
#         logger.error(f"Database query error: {str(e)}")
#         return None


# def insert_or_update_table(conn, doc_hash: str, level_1_id: int, code_str: str, markdown: str) -> None:
#     """Insert or update a record in the tax_valuable_table.

#     Args:
#         conn: Database connection
#         doc_hash: Document hash
#         level_1_id: Level 1 ID
#         code_str: Visualization code
#         markdown: Markdown table content
#     """
#     try:
#         with conn.cursor() as cur:
#             # try to update existing record
#             cur.execute("""
#                 UPDATE tax_valuable_table
#                 SET code_str = %s, markdown = %s
#                 WHERE doc_hash = %s AND level_1_id = %s
#             """, (code_str, markdown, doc_hash, level_1_id))

#             # if no rows were updated, insert a new record
#             if cur.rowcount == 0:
#                 cur.execute("""
#                     INSERT INTO tax_valuable_table
#                     (doc_hash, level_1_id, summary, code_str, markdown)
#                     VALUES (%s, %s, %s, %s, %s)
#                 """, (doc_hash, level_1_id, '', code_str, markdown))
#                 logger.info(f"Created new entry for doc_hash={doc_hash}, level_1_id={level_1_id}")
#             else:
#                 logger.info(f"Updated entry for doc_hash={doc_hash}, level_1_id={level_1_id}")

#         conn.commit()
#     except Exception as e:
#         conn.rollback()
#         logger.error(f"Error adding/updating entry: {str(e)}")


# def main() -> None:
#     """Main function to run the demo."""
#     # Define the PDF path
#     pdf_filename = "773450_2023_Sustainability Report.pdf"
#     pdf_path = os.path.join(PROJECT_ROOT, "cneutral_doc", "pdfs", pdf_filename)

#     # Check if PDF exists
#     if not os.path.exists(pdf_path):
#         logger.error(f"PDF file not found: {pdf_path}")
#         logger.info("You may need to create this directory and add a PDF file for testing.")
#         return

#     # Parse the PDF
#     try:
#         parsed_document = parse_pdf(pdf_path)

#         # Save the parsed document (optional)
#         save_document(
#             parsed_document,
#             f'{SAVE_ROOT}/773450_2023_Sustainability Report_parsed_document.json'
#         )

#         # Filter the tables
#         all_tables = filter_tables(
#             parsed_document,
#             api_key=os.getenv("GOOGLE_API_KEY"),
#             k=1
#         )

#         # Save the tables to files
#         table_contents = save_tables_to_files(all_tables, SAVE_ROOT)

#         # Generate visualizations
#         visualizations = visualize_tables(table_contents, api_key=os.getenv("GOOGLE_API_KEY"))

#         # Save the visualizations to files
#         save_visualizations(visualizations, SAVE_ROOT)

#         # Uncomment to add to database
#         # add_to_database(table_contents, visualizations)

#         logger.info("Demo completed successfully")

#     except Exception as e:
#         logger.error(f"Error in main process: {str(e)}")


# def add_to_database(table_contents: Dict[str, str], visualizations: Dict[str, str]) -> None:
#     """Add visualizations to database (commented out by default).

#     Args:
#         table_contents: Dictionary of table contents by category
#         visualizations: Dictionary of visualization code by category
#     """
#     # Setup database connection parameters
#     doc_hash = "8e6e12d77a17f4c32d6b1b3c21b348b86cc17c29f32f99edf32c198f5b2bd104"
#     e_id, s_id, g_id = 1, 2, 3

#     try:
#         # Connect to database
#         conn = psycopg2.connect(
#             dbname='devdb',
#             user='nair6468',
#             password=os.getenv('DEVDB_PASSWORD'),
#             host='localhost',
#             port=5432
#         )

#         # Get the document ID
#         doc_id = get_doc_by_hash(conn, doc_hash)

#         if doc_id is None:
#             logger.warning(f"No document found with hash {doc_hash}")

#         # Prepare data to insert/update
#         data = [
#             {"level_1_id": e_id, "code_str": visualizations.get('E', ""), "markdown": table_contents.get('E', "")},
#             {"level_1_id": s_id, "code_str": visualizations.get('S', ""), "markdown": table_contents.get('S', "")},
#             {"level_1_id": g_id, "code_str": visualizations.get('G', ""), "markdown": table_contents.get('G', "")}
#         ]

#         # Insert or update each record
#         for d in data:
#             insert_or_update_table(
#                 conn,
#                 doc_hash,
#                 d["level_1_id"],
#                 d["code_str"],
#                 d["markdown"]
#             )

#         logger.info("All data inserted/updated successfully")

#     except Exception as e:
#         logger.error(f"Database connection error: {str(e)}")

#     finally:
#         # Close the connection if it exists
#         if 'conn' in locals() and conn:
#             conn.close()


# if __name__ == "__main__":
#     main()
