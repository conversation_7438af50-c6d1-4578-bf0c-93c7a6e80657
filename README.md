# CNeutral Doc Intelligence Engine (Proprietary)

An analytics engine for ESG (Environmental, Social, and Governance) document processing and analysis. The engine extracts structured metadata and insights from sustainability-related documents using advanced LLM capabilities.

## Features

-   ESG document classification
-   PDF processing capabilities
-   Table and image extraction from PDFs
-   LLM-based qualitative document analysis with ESG factors

## Requirements

-   Python 3.10 or higher
-   Poetry for dependency management

## Development Setup

1. Clone the repository:

```bash
git clone https://github.com/Team-Plato/doc-intelligence-engine

# if you have set up ssh, use this
<NAME_EMAIL>:Team-Plato/doc-intelligence-engine.git

cd doc-intelligence-engine
```

2. Install Poetry (if not already installed):

```bash
curl -sSL https://install.python-poetry.org | python3 -
```

3. Install dependencies:

```bash
poetry install
```

4. Set up pre-commit hooks:

```bash
poetry run pre-commit install
```

## Project Structure

```
.
├── config/                # Configuration files
│   └── config.yaml        # Main configuration
├── src/
│   └── cneutral_doc/     # Main package
│       ├── __init__.py   # Package initialization
│       ├── analyzer/     # Document analysis components
│       │   ├── __init__.py
│       │   └── analyzer.py
│       ├── parser/       # PDF parsing components
│       │   ├── __init__.py
│       │   ├── base.py   # Base parser interfaces
│       │   └── marker_parser.py  # Marker library implementation
│       ├── tagger.py     # Document tagging and classification
│       └── utils.py      # Utility functions
├── tests/
│   └── test_tagger.py    # Test suite for tagger module
├── poetry.lock           # Lock file for dependencies
├── pyproject.toml        # Project configuration and dependencies
└── README.md            # Project documentation
```

## Key Components

### Parser Module

-   `DocumentParser`: Abstract base class for document parsing
-   `MarkerParser`: Implementation using the Marker library for PDF processing
-   Handles text, tables, and image extraction

### Tagger Module

-   `BaseDocTagger`: Abstract base class for document tagging
-   `LLMDocTagger`: Base implementation for LLM-based tagging
-   `OpenAIDocTagger`: OpenAI-specific implementation
-   Supports document classification and metadata extraction

### Data Models

-   `TaggingOutput`: Structured output for document metadata
-   `ParsedDocument`: Container for parsed document data
-   `Page`: Representation of parsed document pages
-   `Table`: Representation of extracted tables

## Development Guidelines

-   Code formatting is handled by Black and isort
-   Use pytest for testing
-   Follow the pre-commit hooks for code quality
-   Python version management via pyenv (currently using 3.10)

## Running Tests

```bash
poetry run pytest
```

## Dependencies

### Main Dependencies

-   marker-pdf: PDF processing
-   langchain: LLM integration
-   langchain-openai: OpenAI integration
-   pdfplumber: PDF text extraction

### Development Dependencies

-   pytest: Testing framework
-   black: Code formatting
-   isort: Import sorting
-   pre-commit: Git hooks

## Code Quality

The project uses several tools to maintain code quality:

-   Black for code formatting
-   isort for import sorting
-   pre-commit hooks for automated checks

## Configuration

The project uses YAML configuration files located in the `config/` directory. Use `utils.read_config()` to access configuration values.

## License

Proprietary License

This project is proprietary software. All rights reserved. Unauthorized copying, modification, distribution, or use of this software, via any medium, is strictly prohibited without express permission from the copyright holder.
